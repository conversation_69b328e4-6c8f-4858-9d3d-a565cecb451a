import time
import os
import sys
import math

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import TOUCH
import time

sensor = None

# PID控制器类
class PIDController:
    def __init__(self, kp, ki, kd, target=0):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.target = target
        self.error_sum = 0
        self.last_error = 0

    def calculate(self, current_value):
        error = self.target - current_value
        self.error_sum += error
        error_diff = error - self.last_error

        output = self.kp * error + self.ki * self.error_sum + self.kd * error_diff
        self.last_error = error

        # 限制积分项防止积分饱和
        if abs(self.error_sum) > 1000:
            self.error_sum = 1000 if self.error_sum > 0 else -1000

        return output

    def set_target(self, target):
        self.target = target

    def reset(self):
        self.error_sum = 0
        self.last_error = 0

# A4纸张识别和激光打靶系统（直接步进电机控制版本）
class A4TargetSystem:
    def __init__(self):
        # A4纸张参数 (210mm x 297mm, 黑胶带宽度1.8cm)
        self.A4_RATIO = 297.0 / 210.0  # 约1.414
        self.A4_RATIO_TOLERANCE = 0.4  # 比例容差，考虑视角和胶带影响
        self.TAPE_WIDTH_MM = 18  # 黑胶带宽度18mm

        # 识别参数 - 针对黑胶带边框优化
        self.min_area = 8000      # 最小面积
        self.max_area = 300000    # 最大面积
        self.rect_threshold = 5000 # 矩形检测阈值

        # 二值化阈值 - 针对黑胶带边框
        self.black_threshold = [(0, 80)]    # 检测黑色胶带
        self.white_threshold = [(120, 255)] # 检测白色A4纸

        # 目标跟踪
        self.target_found = False
        self.target_center = (400, 240)  # 屏幕中心作为初始目标
        self.target_stable_count = 0
        self.required_stable_frames = 5

        # 射击状态
        self.shoot_mode = False
        self.shoot_completed = False

        # 步进电机控制参数
        self.step_num_x = 0  # X轴步进电机当前步数
        self.step_num_y = 0  # Y轴步进电机当前步数
        self.step_status_lst = [[1, 1, 0, 0], [0, 1, 1, 0], [0, 0, 1, 1], [1, 0, 0, 1]]  # 四相步进电机序列

        # PID控制器参数
        self.pid_x = PIDController(kp=0.1, ki=0.05, kd=0.02, target=400)  # X轴PID
        self.pid_y = PIDController(kp=0.1, ki=0.05, kd=0.02, target=240)  # Y轴PID

        # 步进电机引脚（根据引脚定义图配置）
        self.motor_x_pins = []  # X轴步进电机引脚
        self.motor_y_pins = []  # Y轴步进电机引脚

        # 激光笔控制
        self.laser_pin = None
        self.laser_pwm = None

        # 脱机调试系统
        self.debug_mode = False
        self.current_param = 0
        self.param_names = ["L_MIN", "L_MAX", "A_MIN", "A_MAX", "B_MIN", "B_MAX", "GRAY_MIN", "GRAY_MAX", "AREA_MIN", "AREA_MAX"]
        self.debug_params = {
            'L_MIN': 0, 'L_MAX': 100,      # LAB的L通道
            'A_MIN': -128, 'A_MAX': 127,   # LAB的A通道
            'B_MIN': -128, 'B_MAX': 127,   # LAB的B通道
            'GRAY_MIN': 0, 'GRAY_MAX': 80, # 灰度阈值
            'AREA_MIN': 8000, 'AREA_MAX': 300000
        }
        self.adjust_steps = {
            'L_MIN': 1, 'L_MAX': 1,
            'A_MIN': 2, 'A_MAX': 2,
            'B_MIN': 2, 'B_MAX': 2,
            'GRAY_MIN': 1, 'GRAY_MAX': 1,
            'AREA_MIN': 500, 'AREA_MAX': 5000
        }
        self.touch_start_time = 0
        self.long_press_threshold = 1000
        self.touch_count = 0
        self.button_hold_time = 0
        self.last_button = None

        # 稳定性滤波
        self.center_history = []
        self.max_history = 5  # 保存最近5帧的中心点

        # 加载保存的阈值
        self.load_thresholds()

    def init_hardware(self):
        """初始化硬件引脚和PWM"""
        try:
            fpioa = FPIOA()
            print("FPIOA初始化成功")

            # 先尝试初始化激光笔PWM控制
            print("开始初始化激光笔PWM...")
            fpioa.set_function(47, FPIOA.PWM3)
            self.laser_pwm = PWM(3, 50)  # PWM通道3，50Hz频率
            self.laser_pwm.duty(0)  # 初始占空比为0（激光笔关闭）
            self.laser_pwm.enable(1)  # 启用PWM
            print("激光笔PWM初始化成功")

            # 初始化X轴步进电机引脚
            print("开始初始化X轴步进电机引脚...")
            fpioa.set_function(15, FPIOA.GPIO15)  # X轴A相
            fpioa.set_function(17, FPIOA.GPIO17)  # X轴B相
            fpioa.set_function(16, FPIOA.GPIO16)  # X轴C相
            fpioa.set_function(19, FPIOA.GPIO19)  # X轴D相

            self.motor_x_pins = [
                Pin(15, Pin.OUT),  # A相
                Pin(17, Pin.OUT),  # B相
                Pin(16, Pin.OUT),  # C相
                Pin(19, Pin.OUT)   # D相
            ]
            print("X轴步进电机引脚初始化成功")

            # 初始化Y轴步进电机引脚
            print("开始初始化Y轴步进电机引脚...")
            fpioa.set_function(27, FPIOA.GPIO27)  # Y轴A相
            fpioa.set_function(14, FPIOA.GPIO14)  # Y轴B相
            fpioa.set_function(61, FPIOA.GPIO61)  # Y轴C相
            fpioa.set_function(40, FPIOA.GPIO40)  # Y轴D相

            self.motor_y_pins = [
                Pin(27, Pin.OUT),  # A相
                Pin(14, Pin.OUT),  # B相
                Pin(61, Pin.OUT),  # C相
                Pin(40, Pin.OUT)   # D相
            ]
            print("Y轴步进电机引脚初始化成功")

            # 初始化步进电机引脚状态
            print("设置步进电机初始状态...")
            self.set_motor_step(self.motor_x_pins, 0)
            self.set_motor_step(self.motor_y_pins, 0)

            print("硬件初始化完成")
        except Exception as e:
            print(f"硬件初始化失败: {e}")
            # 设置默认值，避免后续调用出错
            self.laser_pwm = None
            self.motor_x_pins = []
            self.motor_y_pins = []

    def set_motor_step(self, motor_pins, step_num):
        """设置步进电机的步进状态"""
        if not motor_pins:
            return

        try:
            step_status = self.step_status_lst[step_num % 4]
            for pin, value in zip(motor_pins, step_status):
                pin.value(value)
        except Exception as e:
            print(f"设置步进电机状态失败: {e}")

    def move_motor_x(self, direction):
        """移动X轴步进电机一步"""
        if not self.motor_x_pins:
            print("X轴步进电机未初始化，无法控制")
            return

        try:
            if direction > 0:
                self.step_num_x = (self.step_num_x + 1) % 4
            elif direction < 0:
                self.step_num_x = (self.step_num_x - 1 + 4) % 4
            self.set_motor_step(self.motor_x_pins, self.step_num_x)
        except Exception as e:
            print(f"X轴步进电机控制失败: {e}")

    def move_motor_y(self, direction):
        """移动Y轴步进电机一步"""
        if not self.motor_y_pins:
            print("Y轴步进电机未初始化，无法控制")
            return

        try:
            if direction > 0:
                self.step_num_y = (self.step_num_y + 1) % 4
            elif direction < 0:
                self.step_num_y = (self.step_num_y - 1 + 4) % 4
            self.set_motor_step(self.motor_y_pins, self.step_num_y)
        except Exception as e:
            print(f"Y轴步进电机控制失败: {e}")

    def control_laser(self, enable):
        """控制激光笔开关"""
        if self.laser_pwm is None:
            print("激光笔PWM未初始化，无法控制")
            return

        try:
            if enable:
                self.laser_pwm.duty(100)  # 100%占空比，激光笔开启
                print("激光笔开启")
            else:
                self.laser_pwm.duty(0)    # 0%占空比，激光笔关闭
                print("激光笔关闭")
        except Exception as e:
            print(f"激光笔控制失败: {e}")

    def aim_target(self, target_x, target_y):
        """使用PID控制瞄准目标"""
        # 计算PID输出
        x_output = self.pid_x.calculate(target_x)
        y_output = self.pid_y.calculate(target_y)

        # 根据PID输出控制步进电机
        # 设定阈值，避免小幅抖动
        x_threshold = 5
        y_threshold = 5

        if abs(x_output) > x_threshold:
            steps_x = int(abs(x_output) / 10)  # 将PID输出转换为步数
            direction_x = 1 if x_output > 0 else -1
            for _ in range(min(steps_x, 5)):  # 限制单次最大步数
                self.move_motor_x(direction_x)
                time.sleep_ms(2)  # 步进电机步进间隔

        if abs(y_output) > y_threshold:
            steps_y = int(abs(y_output) / 10)  # 将PID输出转换为步数
            direction_y = 1 if y_output > 0 else -1
            for _ in range(min(steps_y, 5)):  # 限制单次最大步数
                self.move_motor_y(direction_y)
                time.sleep_ms(2)  # 步进电机步进间隔

    def which_button(self, x, y):
        """判断按下的是哪个按钮"""
        # 返回按钮
        if x < 100 and y < 40:
            return "exit"

        # 参数选择按钮（左侧）
        if x < 120 and y >= 50:
            param_index = (y - 50) // 35
            if param_index < len(self.param_names) and (y - 50) % 35 < 30:
                return f"param_{param_index}"

        # 减少按钮
        if 680 <= x <= 780 and 120 <= y <= 180:
            return "minus"

        # 增加按钮
        if 680 <= x <= 780 and 200 <= y <= 260:
            return "plus"

        return None

    def handle_touch_debug(self, touch_x, touch_y, is_holding=False):
        """处理调试模式下的触摸"""
        if not self.debug_mode:
            return False

        button = self.which_button(touch_x, touch_y)
        if not button:
            self.last_button = None
            self.button_hold_time = 0
            return False

        # 长按加速逻辑
        if button == self.last_button and is_holding:
            self.button_hold_time += 1
        else:
            self.button_hold_time = 0
            self.last_button = button

        # 根据长按时间调整步长
        speed_multiplier = 1
        if self.button_hold_time > 10:
            speed_multiplier = 5
        elif self.button_hold_time > 20:
            speed_multiplier = 10

        if button == "exit":
            self.debug_mode = False
            self.save_thresholds()  # 退出时自动保存
            print("退出调试模式，阈值已保存")
            return True

        elif button.startswith("param_"):
            param_index = int(button.split("_")[1])
            self.current_param = param_index
            return True

        elif button == "minus":
            param_name = self.param_names[self.current_param]
            step = self.adjust_steps[param_name] * speed_multiplier
            min_val = self.get_param_min_value(param_name)
            self.debug_params[param_name] = max(min_val, self.debug_params[param_name] - step)
            self.update_thresholds()
            return True

        elif button == "plus":
            param_name = self.param_names[self.current_param]
            step = self.adjust_steps[param_name] * speed_multiplier
            max_val = self.get_param_max_value(param_name)
            self.debug_params[param_name] = min(max_val, self.debug_params[param_name] + step)
            self.update_thresholds()
            return True

        return False

    def get_param_min_value(self, param_name):
        min_values = {
            'L_MIN': 0, 'L_MAX': 0,
            'A_MIN': -128, 'A_MAX': -128,
            'B_MIN': -128, 'B_MAX': -128,
            'GRAY_MIN': 0, 'GRAY_MAX': 0,
            'AREA_MIN': 100, 'AREA_MAX': 100
        }
        return min_values.get(param_name, 0)

    def get_param_max_value(self, param_name):
        max_values = {
            'L_MIN': 100, 'L_MAX': 100,
            'A_MIN': 127, 'A_MAX': 127,
            'B_MIN': 127, 'B_MAX': 127,
            'GRAY_MIN': 255, 'GRAY_MAX': 255,
            'AREA_MIN': 500000, 'AREA_MAX': 1000000
        }
        return max_values.get(param_name, 1000)

    def save_thresholds(self):
        """保存阈值到文件"""
        try:
            import json
            threshold_data = {
                'debug_params': self.debug_params,
                'timestamp': time.ticks_ms()
            }
            with open('/sdcard/thresholds.json', 'w') as f:
                json.dump(threshold_data, f)
            print("阈值已保存到 /sdcard/thresholds.json")
        except Exception as e:
            print(f"保存阈值失败: {e}")

    def load_thresholds(self):
        """从文件加载阈值"""
        try:
            import json
            with open('/sdcard/thresholds.json', 'r') as f:
                threshold_data = json.load(f)

            if 'debug_params' in threshold_data:
                self.debug_params.update(threshold_data['debug_params'])
                self.update_thresholds()
                print("已加载保存的阈值")
        except Exception as e:
            print(f"加载阈值失败，使用默认值: {e}")

    def smooth_center(self, new_center):
        """平滑中心点，减少抖动"""
        if new_center is None:
            return None

        # 添加新的中心点到历史记录
        self.center_history.append(new_center)

        # 保持历史记录长度
        if len(self.center_history) > self.max_history:
            self.center_history.pop(0)

        # 如果历史记录不足，直接返回当前点
        if len(self.center_history) < 3:
            return new_center

        # 计算加权平均（最新的点权重更大）
        total_weight = 0
        weighted_x = 0
        weighted_y = 0

        for i, (x, y) in enumerate(self.center_history):
            weight = i + 1  # 权重递增
            weighted_x += x * weight
            weighted_y += y * weight
            total_weight += weight

        smooth_x = int(weighted_x / total_weight)
        smooth_y = int(weighted_y / total_weight)

        return (smooth_x, smooth_y)

    def update_thresholds(self):
        """根据调试参数更新阈值"""
        # 更新LAB阈值
        self.lab_threshold = [(
            self.debug_params['L_MIN'], self.debug_params['L_MAX'],
            self.debug_params['A_MIN'], self.debug_params['A_MAX'],
            self.debug_params['B_MIN'], self.debug_params['B_MAX']
        )]
        # 更新灰度阈值
        self.black_threshold = [(self.debug_params['GRAY_MIN'], self.debug_params['GRAY_MAX'])]
        # 更新面积阈值
        self.min_area = self.debug_params['AREA_MIN']
        self.max_area = self.debug_params['AREA_MAX']

    def draw_debug_interface(self, img, preview_img=None):
        if not self.debug_mode:
            return

        # 创建调试界面背景
        img.draw_rectangle(0, 0, 800, 480, color=(50, 50, 50), thickness=2, fill=True)

        # 返回按钮
        img.draw_rectangle(0, 0, 100, 40, color=(255, 100, 100), thickness=2, fill=True)
        img.draw_string_advanced(15, 10, 18, "SAVE&EXIT", color=(0, 0, 0))

        # 实时预览区域 - 中间
        if preview_img:
            try:
                # 简单的预览显示 - 使用文字提示代替图像显示
                img.draw_string_advanced(250, 200, 20, "Preview Active", color=(0, 255, 0))
            except Exception as e:
                print(f"预览显示错误: {e}")
                img.draw_string_advanced(250, 200, 20, "Preview Error", color=(255, 0, 0))
        else:
            # 没有预览图像时显示提示
            img.draw_string_advanced(300, 200, 20, "No Preview", color=(128, 128, 128))

        # 预览区域边框
        img.draw_rectangle(250, 120, 300, 240, color=(255, 255, 255), thickness=2, fill=False)
        img.draw_string_advanced(250, 100, 20, "Real-time Preview", color=(255, 255, 255))

        # 参数选择按钮 - 左侧（更紧凑）
        for i, param_name in enumerate(self.param_names):
            y_pos = 50 + i * 35
            if y_pos > 450:  # 防止超出屏幕
                break
            color = (100, 255, 100) if i == self.current_param else (150, 150, 150)
            img.draw_rectangle(0, y_pos, 120, 30, color=color, thickness=1, fill=True)
            img.draw_string_advanced(5, y_pos + 5, 16, param_name, color=(0, 0, 0))

        # 调整按钮 - 右侧
        # 减少按钮
        img.draw_rectangle(680, 120, 100, 60, color=(255, 100, 100), thickness=2, fill=True)
        img.draw_string_advanced(720, 140, 30, "-", color=(0, 0, 0))

        # 增加按钮
        img.draw_rectangle(680, 200, 100, 60, color=(100, 255, 100), thickness=2, fill=True)
        img.draw_string_advanced(720, 220, 30, "+", color=(0, 0, 0))

        # 显示当前参数值
        current_param = self.param_names[self.current_param]
        current_value = self.debug_params[current_param]
        img.draw_rectangle(580, 300, 200, 60, color=(200, 200, 200), thickness=2, fill=True)
        img.draw_string_advanced(590, 315, 18, f"{current_param}", color=(0, 0, 0))
        img.draw_string_advanced(590, 335, 18, f"Value: {current_value}", color=(0, 0, 0))

        # 显示当前阈值
        img.draw_string_advanced(250, 370, 16, f"LAB: L({self.debug_params['L_MIN']}-{self.debug_params['L_MAX']})", color=(255, 255, 255))
        img.draw_string_advanced(250, 390, 16, f"     A({self.debug_params['A_MIN']}-{self.debug_params['A_MAX']})", color=(255, 255, 255))
        img.draw_string_advanced(250, 410, 16, f"     B({self.debug_params['B_MIN']}-{self.debug_params['B_MAX']})", color=(255, 255, 255))
        img.draw_string_advanced(250, 430, 16, f"GRAY: ({self.debug_params['GRAY_MIN']}-{self.debug_params['GRAY_MAX']})", color=(255, 255, 255))
        img.draw_string_advanced(250, 450, 16, f"AREA: ({self.debug_params['AREA_MIN']}-{self.debug_params['AREA_MAX']})", color=(255, 255, 255))

        # 保存提示
        img.draw_string_advanced(10, 460, 14, "Auto-save on exit", color=(100, 255, 100))

    def handle_debug_keys(self):
        """处理脱机调试按键"""
        current_time = time.ticks_ms()
        if time.ticks_diff(current_time, self.last_key_time) < 200:  # 200ms防抖
            return False

        # 这里需要根据实际硬件添加按键检测
        # 假设有4个按键：上、下、左、右
        key_pressed = False

        if key_pressed:
            self.last_key_time = current_time

        return key_pressed



    def get_preview_image(self, img):
        """获取预览图像 - 显示当前阈值效果"""
        try:
            # 简化预览功能，只显示灰度二值化
            current_param = self.param_names[self.current_param]

            if current_param.startswith('GRAY_'):
                # 灰度阈值预览
                gray_img = img.to_grayscale()
                binary_img = gray_img.binary(self.black_threshold)
                return binary_img.to_rgb565()
            else:
                # 其他情况显示原图
                return img.copy()
        except Exception as e:
            print(f"预览图像错误: {e}")
            return None

    def is_a4_rectangle(self, rect):
        """判断是否为A4纸张矩形"""
        corners = rect.corners()

        # 计算矩形的宽度和高度
        width1 = math.sqrt((corners[1][0] - corners[0][0])**2 + (corners[1][1] - corners[0][1])**2)
        height1 = math.sqrt((corners[3][0] - corners[0][0])**2 + (corners[3][1] - corners[0][1])**2)
        width2 = math.sqrt((corners[2][0] - corners[3][0])**2 + (corners[2][1] - corners[3][1])**2)
        height2 = math.sqrt((corners[2][0] - corners[1][0])**2 + (corners[2][1] - corners[1][1])**2)

        # 取平均值
        avg_width = (width1 + width2) / 2
        avg_height = (height1 + height2) / 2

        # 确保长边作为高度
        if avg_width > avg_height:
            avg_width, avg_height = avg_height, avg_width

        # 检查长宽比
        if avg_height > 0:
            ratio = avg_height / avg_width
            if abs(ratio - self.A4_RATIO) < self.A4_RATIO_TOLERANCE:
                return True
        return False

# 初始化变量
touch = None

try:
    # 系统初始化
    target_system = A4TargetSystem()

    # FPIOA配置
    fpioa = FPIOA()
    fpioa.set_function(53, FPIOA.GPIO53)

    # 硬件初始化
    key = Pin(53, Pin.IN, Pin.PULL_DOWN)

    # 初始化步进电机和激光笔硬件
    target_system.init_hardware()

    # 摄像头初始化
    sensor = Sensor(width=800, height=480)
    sensor.reset()
    sensor.set_framesize(width=800, height=480)
    sensor.set_pixformat(Sensor.RGB565)

    # 显示初始化 - 调整顺序确保正确初始化
    Display.init(Display.ST7701, 800, 480, 0, True)
    MediaManager.init()
    sensor.run()

    # 触摸屏初始化
    touch = TOUCH(0)

    clock = time.clock()
    print("系统初始化完成")

    system_state = "FAST_SEARCH"
    frame_count = 0
    start_time = time.ticks_ms()
    target_locked = False
    shoot_executed = False

    while True:
        clock.tick()
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_0)
        frame_count += 1

        # 触摸检测 - 借鉴参考代码的思想
        touch_points = touch.read()
        if touch_points:
            target_system.touch_count += 1
            if target_system.touch_count > 10:  # 持续触摸10帧进入调试模式
                target_system.debug_mode = True
                target_system.touch_count = 0
        else:
            target_system.touch_count -= 2
            target_system.touch_count = max(0, target_system.touch_count)

        if target_system.debug_mode:
            # 在调试模式下处理触摸
            is_touching = len(touch_points) > 0
            if is_touching:
                for point in touch_points:
                    target_system.handle_touch_debug(point.x, point.y, is_holding=True)
            else:
                target_system.last_button = None
                target_system.button_hold_time = 0

            # 获取预览图像
            preview_img = target_system.get_preview_image(img)
            target_system.draw_debug_interface(img, preview_img)
            Display.show_image(img, x=0, y=0)
            time.sleep_ms(50)  # 调试模式下稍微降低帧率，提高稳定性
            continue

        elapsed_time = time.ticks_diff(time.ticks_ms(), start_time)
        remaining_time = max(0, 2000 - elapsed_time)

        # A4纸张识别
        target_found = False
        best_rect = None
        best_center = None

        # 转换为灰度图像进行矩形检测
        img_gray = img.to_grayscale(copy=True)

        # 使用二值化检测黑色边框
        img_binary = img_gray.binary(target_system.black_threshold)

        # 形态学处理减少抖动和噪点
        try:
            # 开运算：去除小噪点
            img_binary = img_binary.erode(1)
            img_binary = img_binary.dilate(1)
            # 闭运算：填补小空洞
            img_binary = img_binary.dilate(1)
            img_binary = img_binary.erode(1)
        except:
            # 如果形态学操作不支持，跳过
            pass

        # 查找矩形
        rects = img_binary.find_rects(threshold=target_system.rect_threshold)

        if rects:
            for rect in rects:
                # 检查面积
                area = rect.w() * rect.h()
                if area < target_system.min_area or area > target_system.max_area:
                    continue

                # 检查长宽比 (A4纸张比例)
                ratio = max(rect.w(), rect.h()) / min(rect.w(), rect.h())
                if abs(ratio - target_system.A4_RATIO) > target_system.A4_RATIO_TOLERANCE:
                    continue

                # 找到有效的A4目标
                target_found = True
                best_rect = rect
                raw_center = (rect.x() + rect.w()//2, rect.y() + rect.h()//2)
                # 应用平滑滤波减少抖动
                best_center = target_system.smooth_center(raw_center)

                # 绘制检测到的矩形
                corners = rect.corners()
                img.draw_line(corners[0][0], corners[0][1], corners[1][0], corners[1][1], color=(0, 255, 0), thickness=3)
                img.draw_line(corners[1][0], corners[1][1], corners[2][0], corners[2][1], color=(0, 255, 0), thickness=3)
                img.draw_line(corners[2][0], corners[2][1], corners[3][0], corners[3][1], color=(0, 255, 0), thickness=3)
                img.draw_line(corners[3][0], corners[3][1], corners[0][0], corners[0][1], color=(0, 255, 0), thickness=3)

                # 绘制中心点
                img.draw_circle(best_center[0], best_center[1], 10, color=(255, 0, 0), thickness=3, fill=False)
                img.draw_cross(best_center[0], best_center[1], color=(255, 0, 0), size=20, thickness=2)

                break  # 只处理第一个有效目标

        if target_found and not target_locked:
            target_system.target_center = best_center
            target_locked = True
            # 设置PID目标为检测到的目标中心
            target_system.pid_x.set_target(best_center[0])
            target_system.pid_y.set_target(best_center[1])
            print(f"目标锁定: X={best_center[0]}, Y={best_center[1]}")

        if target_found and target_locked:
            # 持续跟踪目标，更新PID目标
            target_system.pid_x.set_target(best_center[0])
            target_system.pid_y.set_target(best_center[1])
            # 执行瞄准控制
            target_system.aim_target(best_center[0], best_center[1])

        # 1.8秒后开始射击准备（开启激光笔）
        if elapsed_time > 1800 and not shoot_executed:
            target_system.control_laser(True)  # 开启激光笔
            print("激光笔开启，准备射击")
            shoot_executed = True

        # 2秒时间到，执行最终射击
        if remaining_time <= 0 and not shoot_executed:
            if not target_locked:
                # 如果没有找到目标，瞄准屏幕中心
                target_system.aim_target(400, 240)
            target_system.control_laser(True)  # 确保激光笔开启
            print("时间到，执行射击")
            shoot_executed = True

        img.draw_string_advanced(10, 10, 25, f"FPS: {clock.fps():.1f}", color=(255, 255, 0))
        img.draw_string_advanced(10, 40, 25, f"Time: {remaining_time}ms", color=(255, 0, 0))

        # 显示触摸提示
        if target_system.touch_count > 0:
            img.draw_string_advanced(10, 70, 20, f"Hold: {target_system.touch_count}/10", color=(255, 255, 0))
        else:
            img.draw_string_advanced(10, 70, 20, "Hold screen for debug", color=(128, 128, 128))

        if target_locked:
            img.draw_string_advanced(10, 100, 20, f"Target: {target_system.target_center}", color=(0, 255, 0))

        # 显示步进电机状态
        img.draw_string_advanced(10, 130, 18, f"Motor X: {target_system.step_num_x}, Y: {target_system.step_num_y}", color=(0, 255, 255))

        # 显示激光笔状态
        laser_status = "ON" if shoot_executed else "OFF"
        img.draw_string_advanced(10, 150, 18, f"Laser: {laser_status}", color=(255, 0, 0) if shoot_executed else (128, 128, 128))

        if shoot_executed:
            img.draw_string_advanced(10, 170, 25, "SHOT!", color=(255, 0, 0))

        center_x, center_y = 400, 240
        img.draw_line(center_x-10, center_y, center_x+10, center_y, color=(128, 128, 128), thickness=1)
        img.draw_line(center_x, center_y-10, center_x, center_y+10, color=(128, 128, 128), thickness=1)

        if target_found:
            img.draw_rectangle(best_rect.x(), best_rect.y(), best_rect.w(), best_rect.h(),
                             color=(0, 255, 0), thickness=2)
            img.draw_circle(best_center[0], best_center[1], 3, color=(255, 0, 0), thickness=2)

        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    if touch is not None:
        touch.deinit()
    # 清理硬件资源
    try:
        target_system.control_laser(False)  # 关闭激光笔
        if target_system.laser_pwm:
            target_system.laser_pwm.deinit()
    except:
        pass
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
